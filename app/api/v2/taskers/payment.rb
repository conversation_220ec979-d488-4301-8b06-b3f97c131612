# frozen_string_literal: true

require 'sinatra/json'
require 'sinatra/namespace'

module Api
  module V2
    module Taskers
      # Taskers Payments endpoint for tasker_app api.
      #
      class Payment < Sinatra::Base # rubocop:disable Metrics/ClassLength
        PAYOUT_FREQUENCIES = %w[full on_demand weekly].freeze

        register Sinatra::Namespace
        register Api::Services::Warden
        set :warden_scope, :tasker

        # rubocop:disable Metrics/BlockLength

        namespace '/taskers/payments' do
          before { authenticate_tasker }
          after { response.body = json(response.body) }

          get do
            tasker = current_user(:tasker)
            payments = payment_entries(tasker)

            full_payout = assemble_payout(tasker, :full)
            weekly_payout = assemble_payout(tasker, :weekly)
            on_demand_payout = assemble_payout(tasker, :on_demand)
            future_payout = assemble_payout(tasker, :future)
            withdraw = Payout::Withdraw.new(tasker:, payout_package: on_demand_payout)

            payments << build_payment(full_payout)
            payments << build_payment(weekly_payout)
            payments << build_payment(on_demand_payout)
            payments << build_payment(future_payout) if future_payout.present?

            {
              data: {
                withdraw: {
                  fee: withdraw.fee_amount,
                  unavailable_motives: withdraw.unallowed_withdraw
                },
                payments: payments.map do |payment|
                  {
                    id: payment.id.to_s,
                    number: payment.number,
                    amount: payment.value,
                    type: payment.type,
                    payout_state: payment.payout_state,
                    created_at: payment.created_at
                  }
                end
              }
            }
          end

          post('/withdraw') do
            return halt(500) if ENV.fetch('TASKER_PAYMENT_WITHDRAW_WITH_FORCED_UNKNOWN_ERROR', 'false') == 'true'

            payout = Payout::Processor.new(current_user(:tasker), payment_frequency: :on_demand).pay!
            successful_payout = payout.instance_of? ::Entry

            {
              data: {
                success: successful_payout,
                failure_motives: successful_payout ? [] : payout
              }
            }
          end

          get('/:id/entries') do
            tasker = current_user(:tasker)
            future_payment = PAYOUT_FREQUENCIES.include?(params['id'])
            payment_entry = ::Entry.find(params['id']) unless future_payment

            halt(404) if payment_entry.blank? && !future_payment

            entries = payment_entry ? related_entries(payment_entry) : assemble_payout(tasker, params['id']).entries

            {
              data: {
                entries: entries.map do |entry|
                  {
                    id: entry.id.to_s,
                    amount: entry.value,
                    type: entry.type,
                    description: Descriptors::Entry.new(entry).describe,
                    created_at: entry.created_at
                  }
                end
              }
            }
          end

          namespace '/future-earnings' do
            get do
              tasker = current_user(:tasker)
              future_earnings = Payout::FutureEarnings.new(tasker)

              {
                data: {
                  total_payout: future_earnings.amount,
                  future_payments: future_earnings.payment_dates.map do |date|
                    previous_week = whole_previous_week(date)

                    {
                      payout_date: date.to_date.to_s,
                      job_count: future_earnings.jobs(time_span: previous_week).count,
                      week_payout: future_earnings.amount(time_span: previous_week)
                    }
                  end
                }
              }
            end

            get('/:payout_date') do
              tasker = current_user(:tasker)
              payout_date = Time.zone.parse(params['payout_date'])

              halt(404) unless valid_payout_date?(payout_date)

              previous_week = whole_previous_week(payout_date)
              future_earnings = Payout::FutureEarnings.new(tasker)

              {
                data: {
                  payout_date:,
                  payout_amount: future_earnings.amount(time_span: previous_week),
                  beginning_of_week: previous_week.first,
                  end_of_week: previous_week.last,
                  jobs: future_earnings.jobs(time_span: previous_week).order_by(%i[date asc]).map do |job|
                    job_tasker = job.job_taskers.find { |jt| jt.tasker_id == tasker.id }

                    payouts = [{
                      id: job.id.to_s,
                      date: job.date,
                      payout: job_tasker.final_payout,
                      description: I18n.t(
                        'api.job', user_name: job.user.name.split.first, job_date: job.date.strftime('%d/%m')
                      )
                    }]

                    next payouts unless job_tasker.recurrent_bonus?

                    # Bonus payout is being available as a job for now, until we turn all of this into entries
                    #   or the app knows how to work with the bonus inside the job hash above.
                    #
                    payouts << {
                      id: "bonus:#{job.id}",
                      date: job.date,
                      payout: job_tasker.bonus_payout,
                      description: Descriptors::Entry.new(bonus_entry(job_tasker)).describe
                    }
                  end.flatten
                }
              }
            end
          end
        end

        # rubocop:enable Metrics/BlockLength

        private

        def authenticate_tasker
          authenticate scope: :tasker
          halt(403, { message: 'Not authorized' }) if current_user(:tasker).blank?
        end

        def related_entries(payment) = ::Entry.where(payment_correlation_id: payment.number)

        def valid_payout_date?(date) = date.beginning_of_week.future? && date.wednesday?

        def whole_previous_week(date) = Payout.week_range_for(date, span: 'whole_week')

        def payment_entries(tasker)
          ::Entry
            .where(tasker:, type: :payment)
            .desc(:created_at)
            .where(:created_at.gte => 1.month.ago.beginning_of_day)
            .to_a
        end

        def build_payment(package)
          ::Entry.new(
            id: package.payment_frequency.to_s,
            number: SecureRandom.uuid,
            value: package.amount,
            type: 'payment',
            payout_state: package.payment_frequency.to_s,
            created_at: Time.current
          )
        end

        def assemble_payout(tasker, payment_frequency)
          Payout::AvailableEntries.for(tasker).assemble_payout(payment_frequency.to_sym)
        end

        def bonus_entry(job_tasker)
          ::Entry.new(
            tasker_id: job_tasker.tasker.id,
            job_id: job_tasker.job.id,
            user_id: job_tasker.job.user_id,
            value: job_tasker.bonus_payout,
            bonus_type: :recurrency,
            type: :credit,
            actor: :system
          )
        end
      end
    end
  end
end
