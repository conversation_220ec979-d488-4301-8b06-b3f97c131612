/*global Module, PLUTON, _ */

Module("PLUTON.modules.incidentDatatable", function (module) {
  module.fn.initialize = function (table) {
    this.table = $(table);

    this.requestURL = "/incidents.json";

    this.jobId = this.table.data('job-id') || "";
    this.taskerId = this.table.data('tasker-id') || "";

    if (this.jobId !== "") {
      this.requestURL += "?job_id=" + this.jobId;
    } else if (this.taskerId !== "") {
      this.requestURL += "?tasker_id=" + this.taskerId;
    }

    $(table).dataTable(this._tableOptions());
  };

  module.fn._tableOptions = function () {
    return {
      "language": {
        "lengthMenu": "_MENU_ resultados por página",
        "zeroRecords": "Nenhum resultado encontrado",
        "info": "Exibindo _PAGE_ resultados de _PAGES_",
        "infoEmpty": "Nenhum resultado disponível",
        "infoFiltered": "(filtered from _MAX_ total records)"
      },
      "serverSide": true,
      "processing": true,
      "ajax": this.requestURL,
      "order": [[0, "desc"]],
      "columns": [
        { "data": "incident_at" },
        { "data": "job" },
        { "data": "tasker" },
        { "data": "time_delta" },
        { "data": "type" },
        { "data": "severity" },
        { "data": "show_remove_button" }
      ],
      "columnDefs": [
        {
          "targets": 0,
          "data": "incident_at",
          "render": function (data, type, full, meta) {
            return '<a href="' + full._links['show']['href'] + '">' + data + '</a>';
          }
        },
        {
          "targets": 1,
          "data": "job",
          "render": function (data, type, full, meta) {
            return '<a href="' + full._links['job']['show']['href'] + '">' + data + '</a>';
          }
        },
        {
          "targets": 2,
          "data": "tasker",
          "render": function (data, type, full, meta) {
            return '<a href="' + full._links['tasker']['show']['href'] + '">' + data + '</a>';
          }
        },
        {
          "targets": 4,
          "data": "type",
          "render": function (data, type, full, meta) {
            if (full.type === 'Falta') {
              return '<span class="label label-danger">' + data + '</span>';
            } else if (full.type === 'Atraso') {
              return '<span class="label label-warning">' + data + '</span>';
            } else if (full.type === 'Cancelamento') {
              return '<span class="label label-info">' + data + '</span>';
            } else {
              return '<span class="label label-default">' + data + '</span>';
            }
          }
        },
        {
          "targets": 5,
          "data": "severity",
          "render": function (data, type, full, meta) {
            var labelType;
            switch (full.severity) {
              case 'Normal':
                labelType = 'warning';
                break;
              case 'Alta':
                labelType = 'danger';
                break;
            }
            return '<span class="label label-' + labelType + '">' + data + '</span>';
          }
        },
        {
          "targets": 6,
          "data": "show_remove_button",
          "render": function (data, type, full, meta) {
            if (!full.show_remove_button) return '<span></span>';
            return "<a href=\"/incidents/" + full.id + "\" data-method=\"delete\" data-confirm=\"Tem certeza que deseja remover este incidente?\" class=\"btn btn-danger btn-xs\" rel=\"nofollow\">Remover</a>";
          }
        }
      ]
    };
  };
});
