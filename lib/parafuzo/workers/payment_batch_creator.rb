# frozen_string_literal: true

module Workers
  # Generates a payments batch and send it to Transfeera.
  #
  class PaymentBatchCreator < Base
    include Resque::Plugins::UniqueJob

    PAYMENT_BATCH_CREATOR_WORKER_RUNNING = ENV.fetch('PAYMENT_BATCH_CREATOR_WORKER_RUNNING', true)

    # <PERSON><PERSON> will run using this queue name.
    #
    @queue = :payment

    # It'll be called by resque to run the job.
    #
    def run
      return unless Helpers::Boolean.cast(PAYMENT_BATCH_CREATOR_WORKER_RUNNING)

      transfers = entries.batch_size(100).map { |entry| format(entry) }
      return if transfers.blank?

      Parafuzo::Core::Queue.publish('payments-do-pay', { transfers: transfers })
    end

    private

    # Query all the payable entries newer than two weeks ago.
    #
    # @return [Mongoid::Criteria]
    #
    def entries
      Entry.includes(:tasker).where(
        type: :payment,
        payout_state: 'processing',
        gateway: :transfeera,
        payment_batch_name: nil,
        :created_at.gte => 2.weeks.ago
      )
    end

    # Format an entry to fit the Payments service specifications.
    #
    # @return [Hash]
    #
    def format(entry)
      tasker = entry.tasker
      bank = tasker.bank
      { entry_id: entry.id.to_s, tasker_name: tasker.name, tasker_cpf: tasker.cpf, tasker_email: tasker.email,
        bank_code: bank.code, bank_agency: bank.agency, bank_account: bank.account, bank_account_type: bank.type,
        value: (entry.value.round(2) * 100).round }
    end
  end
end
