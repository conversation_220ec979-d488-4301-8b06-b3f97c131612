# frozen_string_literal: true

require 'spec_helper'

describe Workers::PaymentBatchCreator do
  let(:topic) { 'payments-do-pay' }

  before do
    Timecop.freeze
    allow(Parafuzo::Core::Queue).to receive(:publish)
  end

  after { Timecop.return }

  context 'when there are no entries' do
    before { described_class.perform }

    it 'does not process anything' do
      expect(Parafuzo::Core::Queue).not_to have_received(:publish)
    end
  end

  context 'when there are payable entries' do
    let!(:tasker) { create(:tasker, banks: [build(:bank)]) }
    let!(:entry1) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :transfeera, value: 100.009)
    end
    let!(:entry2) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :transfeera, value: 100.02)
    end

    before { described_class.perform }

    it 'includes all the payable entries' do
      expect(Parafuzo::Core::Queue)
        .to(have_received(:publish)
          .with(topic, hash_including(transfers: array_including(hash_including(entry_id: entry1.id, value: 10_001),
                                                                 hash_including(entry_id: entry2.id, value: 10_002)))))
    end
  end

  context 'when there are unpayable entries' do
    let!(:tasker) { create(:tasker, banks: [build(:bank)]) }
    let!(:iugu_entry) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :iugu)
    end
    let!(:out_of_range_entry) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :transfeera,
                                 created_at: 2.weeks.ago - 1.second)
    end
    let!(:already_processed_entry) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :transfeera,
                                 payment_batch_name: 'some_batch_name')
    end

    before { described_class.perform }

    it 'does not includes unpayable entries' do
      expect(Parafuzo::Core::Queue)
        .not_to have_received(:publish)
        .with(topic, hash_including(transfers: array_including(hash_including(entry_id: iugu_entry.id))))
    end

    it 'does not includes out of range entries' do
      expect(Parafuzo::Core::Queue)
        .not_to have_received(:publish)
        .with(topic, hash_including(transfers: array_including(hash_including(entry_id: out_of_range_entry.id))))
    end

    it 'does not includes entries that has been included in some other batch (`payment_batch_name` not null)' do
      expect(Parafuzo::Core::Queue)
        .not_to have_received(:publish)
        .with(topic, hash_including(transfers: array_including(hash_including(entry_id: already_processed_entry.id))))
    end
  end

  context 'when the worker is disabled via environment variable' do
    let!(:tasker) { create(:tasker, banks: [build(:bank)]) }
    let!(:entry) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :transfeera, value: 100.0)
    end

    before do
      stub_const('Workers::PaymentBatchCreator::PAYMENT_BATCH_CREATOR_WORKER_RUNNING', 'false')
      described_class.perform
    end

    it 'does not process any entries' do
      expect(Parafuzo::Core::Queue).not_to have_received(:publish)
    end
  end

  context 'when the worker is enabled via environment variable' do
    let!(:tasker) { create(:tasker, banks: [build(:bank)]) }
    let!(:entry) do
      create(:entry, :by_system, tasker: tasker, type: :payment,
                                 payout_state: 'processing', gateway: :transfeera, value: 100.0)
    end

    before do
      stub_const('Workers::PaymentBatchCreator::PAYMENT_BATCH_CREATOR_WORKER_RUNNING', 'true')
      described_class.perform
    end

    it 'processes the entries' do
      expect(Parafuzo::Core::Queue)
        .to have_received(:publish)
        .with(topic, hash_including(transfers: array_including(hash_including(entry_id: entry.id))))
    end
  end
end
